using Bookify.Application.Abstractions.Authentication;
using Bookify.Domain.Entities.Abstractions;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Bookify.Infrastructure.Repositories;

public abstract class UserAwareRepository<TEntity, TEntityId> : Repository<TEntity, TEntityId>
    where TEntity : Entity<TEntityId>
    where TEntityId : class
{
    protected readonly IUserContext _userContext;

    protected UserAwareRepository(ApplicationDbContext dbContext, IUserContext userContext) 
        : base(dbContext)
    {
        _userContext = userContext;
    }

    // Abstract method that derived classes must implement to specify how to filter by user
    protected abstract Expression<Func<TEntity, bool>> GetUserFilter();

    // Get entity by ID, but only if it belongs to the current user
    public async Task<TEntity?> GetByIdForCurrentUserAsync(TEntityId id, CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(userFilter)
            .FirstOrDefaultAsync(cancellationToken);
    }

    // Get all entities for the current user
    public async Task<IEnumerable<TEntity>> GetAllForCurrentUserAsync(CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(userFilter)
            .ToListAsync(cancellationToken);
    }

    // Get entities with additional filtering for the current user
    public async Task<IEnumerable<TEntity>> GetForCurrentUserAsync(
        Expression<Func<TEntity, bool>> additionalFilter, 
        CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(userFilter)
            .Where(additionalFilter)
            .ToListAsync(cancellationToken);
    }

    // Check if entity exists and belongs to current user
    public async Task<bool> ExistsForCurrentUserAsync(TEntityId id, CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(userFilter)
            .AnyAsync(cancellationToken);
    }
}
