using Bookify.Application.Abstractions.Messaging;
using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.Entities.Bookings;

namespace Bookify.Application.Bookings.GetBooking;

// Option 1: Repository-Based Approach (Recommended for Clean Architecture)
internal sealed class GetBookingQueryHandler : IQueryHandler<GetBookingQuery, BookingResponse>
{
    private readonly IBookingRepository _bookingRepository;

    public GetBookingQueryHandler(IBookingRepository bookingRepository)
    {
        _bookingRepository = bookingRepository;
    }

    public async Task<Result<BookingResponse>> Handle(GetBookingQuery request, CancellationToken cancellationToken)
    {
        // Use the user-aware repository method - automatically filters by current user
        var booking = await _bookingRepository.GetByIdForCurrentUserAsync(
            new BookingId(request.BookingId),
            cancellationToken);

        if (booking is null)
            return Result.Failure<BookingResponse>(BookingErrors.NotFound);

        // Map to response (you might want to use AutoMapper or similar)
        var response = new BookingResponse
        {
            Id = booking.Id.Value,
            ApartmentId = booking.ApartmentId.Value,
            UserId = booking.UserId.Value,
            Status = (int)booking.Status,
            PriceAmount = booking.PriceForPeriod.Amount,
            PriceCurrency = booking.PriceForPeriod.Currency.Code,
            CleaningFeeAmount = booking.CleaningFee.Amount,
            CleaningFeeCurrency = booking.CleaningFee.Currency.Code,
            AmenitiesUpChargeAmount = booking.AmenitiesUpCharge.Amount,
            AmenitiesUpChargeCurrency = booking.AmenitiesUpCharge.Currency.Code,
            TotalPriceAmount = booking.TotalPrice.Amount,
            TotalPriceCurrency = booking.TotalPrice.Currency.Code,
            DurationStart = booking.Duration.Start,
            DurationEnd = booking.Duration.End,
            CreatedOnUtc = booking.CreatedOnUtc
        };

        return response;
    }
}
